package com.udaan.cart.core.common.providers

import com.google.inject.Inject
import com.google.inject.Singleton
import com.udaan.cart.core.context.CartContext
import com.udaan.cart.core.context.CartContextKey
import com.udaan.cart.core.domain.models.ListingProductItem
import com.udaan.common.utils.kotlin.logger
import com.udaan.config.client.BusinessConfigClient
import com.udaan.constraint.client.ConstraintClient
import com.udaan.constraint.models.dtos.AdditionalDataKey
import com.udaan.constraint.models.dtos.DeliveryChargeRequestDto
import com.udaan.constraint.models.dtos.DeliveryChargeResponseDto
import com.udaan.constraint.models.dtos.ListingRequest
import com.udaan.constraint.models.dtos.OrderFlow
import com.udaan.proto.models.ModelV1
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withTimeout

interface ConstraintProvider {

    suspend fun evaluateDeliveryCharges(
        cartContext: CartContext
    ): DeliveryChargeResponseDto?
}

@Singleton
class ConstraintProviderImpl @Inject constructor(
    private val constraintClient: ConstraintClient,
    private val businessConfigClient: BusinessConfigClient
) : ConstraintProvider {

    companion object {
        private val logger by logger()
        private const val TIMEOUT_MILLIS_DEFAULT = 100L
        private const val ENABLE_DELIVERY_CHARGE_OFFER = "enable_delivery_charge_offer"
    }

    override suspend fun evaluateDeliveryCharges(cartContext: CartContext): DeliveryChargeResponseDto? {
        val cart = cartContext.getCart()
        val buyerOrgUnitId = cartContext.getValue<String>(CartContextKey.BUYER_ORG_UNIT_ID)
        val detailedCart = cartContext.getDetailedCart(orgUnitId = buyerOrgUnitId, fetchLatestPrices = true, skipOOSItems = true)
        val availableItems = detailedCart?.cart?.items ?: emptyList()
        val deliveryChargeOfferEnabled = isDeliveryChargeOfferEnable()
        if(availableItems.isEmpty()) return null
        val deliveryCharge = cart?.buyerId?.let { buyerId ->
            val categoryGroupId = cart.getCategoryGroupId()
            val orderTotalInPaise = detailedCart?.totalAmountForItems(availableItems) ?: 0

            // Ensure buyerOrgUnitId is set in context before calling fetchDeliveryChargeIdentifier
            val effectiveBuyerOrgUnitId = buyerOrgUnitId
                ?: cartContext.getBuyerOrg()?.orgAccount?.headOfficeOrgUnitRef
            effectiveBuyerOrgUnitId?.let { orgUnitId ->
                cartContext.setValue(CartContextKey.BUYER_ORG_UNIT_ID, orgUnitId)
            }

            val deliveryChargeIdentifier = cartContext.fetchDeliveryChargeIdentifier()
            constraintClient.calculateDeliveryCharges(
                request = DeliveryChargeRequestDto(
                    buyerOrgId = buyerId,
                    platformId = ModelV1.SellingPlatform.UDAAN_MARKETPLACE,
                    categoryGroupId = categoryGroupId,
                    orderTotalInPaise = orderTotalInPaise,
                    toOrgUnitId = effectiveBuyerOrgUnitId,
                    additionalData = mapOf(AdditionalDataKey.FETCH_DELIVERY_CHARGE_OFFER to deliveryChargeOfferEnabled),
                    orderFlow = OrderFlow.PRE_ORDER,
                    deliveryChargeCartIdentifier = deliveryChargeIdentifier,
                    listingRequests = availableItems.map { item ->
                        val listingItem = item.product as ListingProductItem
                        ListingRequest(
                            listingId = listingItem.listingId,
                            salesUnitId = listingItem.salesUnitId,
                            quantity = listingItem.quantity,
                            priceInPaise = detailedCart?.getItemTotalWithTax(item)
                        )
                    }
                )
            ).executeAwait()
        }

        return deliveryCharge
    }


    /**
     * Checks if the delivery charge offer feature is enabled by fetching the corresponding
     * configuration value from the business configuration client.
     * In case of a timeout or error during the operation, logs the error and returns false.
     *
     * @return a Boolean value indicating whether the delivery charge offer feature is enabled (true)
     *         or disabled (false).
     */
    private suspend fun isDeliveryChargeOfferEnable(): Boolean {
        return kotlin.runCatching {
            withTimeout(TIMEOUT_MILLIS_DEFAULT) {
                businessConfigClient.getBooleanAsync(ENABLE_DELIVERY_CHARGE_OFFER).await()
            } ?: false
        }.getOrElse { e ->
            logger.error("[isDeliveryChargeOfferEnable] Failed to load enable_delivery_charge_offer " +
                    "config: ${e.message}", e)
            false
        }
    }
}
