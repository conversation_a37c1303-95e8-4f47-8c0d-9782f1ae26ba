package com.udaan.cart.core.context

import arrow.core.Either
import com.fasterxml.jackson.databind.ObjectMapper
import com.udaan.cart.core.common.helpers.ConfigServiceHelper
import com.udaan.cart.core.common.providers.*
import com.udaan.cart.core.common.providers.promotions.PromotionsProvider
import com.udaan.cart.core.common.helpers.isMeat
import com.udaan.cart.core.common.providers.OrgIdentityProvider
import com.udaan.cart.core.common.providers.promotions.SchemesProvider
import com.udaan.cart.core.db.repo.CartReadRepo
import com.udaan.cart.core.db.repo.models.DbSelectorData
import com.udaan.cart.core.domain.models.*
import com.udaan.cart.core.domain.models.CartItem
import com.udaan.cart.core.exceptions.LevyGenerationException
import com.udaan.cart.models.common.*
import com.udaan.catalog.client.CatalogServiceClient
import com.udaan.catalog.client.CategoryGroupV2
import com.udaan.catalog.client.helpers.VerticalCache
import com.udaan.catalog.models.ModelV2
import com.udaan.catalog.representations.ApiV2
import com.udaan.common.utils.kotlin.logger
import com.udaan.common.utils.parallelMap
import com.udaan.model.orgs.OrgInternalIdentityModel
import com.udaan.orderform.common.models.ListingAvailability
import com.udaan.orderform.common.models.ListingData
import com.udaan.orderform.common.providers.*
import com.udaan.proto.models.ModelV1
import com.udaan.proto.representations.OrgV1
import com.udaan.user.client.RedisOrgRepository
import kotlinx.coroutines.future.await
import java.security.MessageDigest
import kotlin.time.DurationUnit
import kotlin.time.measureTimedValue
import com.udaan.cart.core.common.helpers.TimeHelper

enum class CartContextKey {
    PROMOTION_CONTEXT,
    COUPON_IDS,

    @Suppress("unused")
    PROMOTION_FLAGS,

    @Suppress("unused")
    BUYER_ORG_UNIT_ID,
    CPOD_ENABLED,
    CHECKOUT_CALL
}

interface CartContext : BaseContext {
    suspend fun getCartId(): String
    suspend fun getCart(): Cart?
    suspend fun getDetailedCart(
        orgUnitId: String?,
        skipOOSItems: Boolean = false,
        fetchLatestPrices: Boolean = false,
        fetchRiderDetails: Boolean = false,
        orderReadyForCheckout: Boolean = false
    ): DetailedCart?

    suspend fun getListingsMap(): Map<String, ModelV2.TradeListing>
    suspend fun getBuyerOrg(): OrgV1.OrgAccountExtendedResponse?
    suspend fun getBuyerOrgUnit(orgUnitId: String?): ModelV1.OrgUnit?

    suspend fun getBuyerOrgIdentityModel(): OrgInternalIdentityModel?
    suspend fun getCartWithLatestPrices(
        orgUnitId: String?,
        fetchRiderDetails: Boolean = false,
        orderReadyForCheckout: Boolean = false
    ): Pair<Cart, Map<String, String>>?
    suspend fun getCartWithPromos(
        orgUnitId: String?,
        skipOOSItems: Boolean = false,
        fetchRiderDetails: Boolean = false,
        orderReadyForCheckout: Boolean = false
    ): DetailedCart?

    suspend fun getPreloadedCartWithPromos(): DetailedCart?

    fun getItemsWithInvalidPrice(): List<CartItem>
    suspend fun getUnavailabilityInfo(
        buyerOrgUnitId: String?,
        cart: Cart? = null,
        forceFetch: Boolean = false
    ): List<ListingAvailability>
    suspend fun getAvailableItems(
        existingCart: Cart?,
        buyerOrgUnitId: String?,
        forceFetch: Boolean = false
    ): List<CartItem>
    suspend fun <T> setValue(key: CartContextKey, value: T)
    suspend fun <T> getValue(key: CartContextKey): T?
    suspend fun getPackagingUnitDetails(itemId: String): PackagingUnit?
    suspend fun getRiderPromos(): Map<String, Map<String, Int>>

    suspend fun fetchDeliveryChargeIdentifier(): String
}

class CartContextImpl internal constructor(
    cart: Cart? = null,
    private val cartId: String,
    private val cartReadRepo: CartReadRepo,
    private val orgProvider: OrgProvider,
    private val orgIdentityProvider: OrgIdentityProvider,
    private val catalogProvider: CatalogProvider,
    private val pricingProvider: PriceProvider,
    private val pricingContextInterpreter: PriceContextInterpreter,
    private val levyProvider: LevyProvider,
    private val inventoryProvider: InventoryProvider,
    private val orgRepository: RedisOrgRepository,
    private val promotionsProvider: PromotionsProvider,
    private val catalogServiceClient: CatalogServiceClient,
    private val verticalCache: VerticalCache,
    private val schemesProvider: SchemesProvider,
    private val hubProvider: HubProvider,
    private val configServiceHelper: ConfigServiceHelper,
    private val objectMapper: ObjectMapper,
    private val timeHelper: TimeHelper
) : CartContext {
    private var buyerCart: Cart? = cart
    private var buyerOrg: OrgV1.OrgAccountExtendedResponse? = null
    private var listingsMap: Map<String, ModelV2.TradeListing>? = null
    private var listingAvailability: List<ListingAvailability>? = null
    private val valuesMap: MutableMap<CartContextKey, Any> = mutableMapOf()
    private var detailedCart: DetailedCart? = null
    private var cartWithPromos: DetailedCart? = null
    private var inventoryLoaded = false
    private var availableItemsLoaded = false
    private var packagingUnitsMap = emptyMap<String, PackagingUnit?>()
    private val riderPromosMap = mutableMapOf<String, Map<String, Int>>()
    private var itemsWithInvalidPrice = listOf<CartItem>()
    private var availableCartItems = listOf<CartItem>()

    override suspend fun fetchDeliveryChargeIdentifier(): String {
        val cart = getCart() ?: throw IllegalStateException("Cart is not available in context")
        val buyerOrgUnitId = getValue<String>(CartContextKey.BUYER_ORG_UNIT_ID)
            ?.takeIf { it.isNotBlank() }
            ?: throw IllegalStateException("buyerOrgUnitId is not available in context. Ensure BUYER_ORG_UNIT_ID is set in CartContext before calling fetchDeliveryChargeIdentifier()")

        return generateDeliveryChargeIdentifier(buyerOrgUnitId, cart.selection)
    }
    
    private fun generateDeliveryChargeIdentifier(buyerOrgUnitId: String, cartSelection: CartSelection?): String {
        // Use TimeHelper for consistent date formatting across the application
        val dateStr = timeHelper.getCurrentDate()
        val cartSelectionStr = cartSelection?.let { it::class.simpleName ?: "" } ?: ""
        val input = "$buyerOrgUnitId$dateStr$cartSelectionStr"

        logger.debug("Generating delivery charge identifier for input: buyerOrgUnitId=$buyerOrgUnitId, date=$dateStr, cartSelection=$cartSelectionStr")

        val md = MessageDigest.getInstance(HASH_ALGORITHM)
        val digest = md.digest(input.toByteArray(Charsets.UTF_8))

        val hexString = digest.joinToString("") { "%02x".format(it) }
        val identifier = hexString.take(IDENTIFIER_LENGTH)

        logger.debug("Generated delivery charge identifier: $identifier")
        return identifier
    }

    companion object {
        private val logger by logger()
        private const val INDEPENDENT = "INDEPENDENT"
        private const val PARENT = "PARENT"
        private const val FREEBIE = "FREEBIE"
        private const val MEAT_TO_FIRST_PARTY_EXP = "meat-to-first-party-experiment"
        private const val catalogBatch = 5
        private const val PARALLELISM_FACTOR = 10
        private const val IDENTIFIER_LENGTH = 16
        private const val HASH_ALGORITHM = "SHA-256"
    }

    override suspend fun getCartId(): String {
        return cartId
    }

    override suspend fun getCart(): Cart? {
        if (buyerCart == null) {
            buyerCart = cartReadRepo.findCartById(
                DbSelectorData(
                    cartId = cartId,
                    buyerId = "",
                    platformId = "",
                    cartSelection = BuyerBasedSelection(),
                )
            )
        }
        return buyerCart
    }

    override suspend fun getDetailedCart(
        orgUnitId: String?,
        skipOOSItems: Boolean,
        fetchLatestPrices: Boolean,
        fetchRiderDetails: Boolean,
        orderReadyForCheckout: Boolean
    ): DetailedCart? {
        return if (fetchLatestPrices || detailedCart == null) {
            logger.info("Fetching pricing details with rider details : $fetchRiderDetails")
            detailedCart = getCartWithLatestPrices(
                orgUnitId = orgUnitId,
                fetchRiderDetails = fetchRiderDetails,
                orderReadyForCheckout = orderReadyForCheckout
            )?.let { (cart, listingIdToPricingAuditIds) ->
                val leviesMap = fetchLevies(cart)
                DetailedCart(
                    cart = cart,
                    leviesMap = leviesMap,
                    itemsWithInvalidPrice = itemsWithInvalidPrice,
                    listingIdToPricingAuditIds = listingIdToPricingAuditIds
                )
            }
            detailedCart?.let {
                if (skipOOSItems) {
                    val cart = cartWithAvailableItems(
                        cart = it.cart,
                        buyerOrgUnitId = orgUnitId,
                        forceFetch = true
                    )
                    val leviesMap = fetchLevies(cart)
                    DetailedCart(
                        cart = cart,
                        leviesMap = leviesMap,
                        itemsWithInvalidPrice = itemsWithInvalidPrice,
                        listingIdToPricingAuditIds = it.listingIdToPricingAuditIds
                    )
                } else it
            }
        } else {
            detailedCart?.let {
                if (skipOOSItems) {
                    val cart = cartWithAvailableItems(
                        cart = it.cart,
                        buyerOrgUnitId = orgUnitId,
                        forceFetch = true
                    )
                    val leviesMap = fetchLevies(cart)
                    DetailedCart(
                        cart = cart,
                        leviesMap = leviesMap,
                        itemsWithInvalidPrice = itemsWithInvalidPrice,
                        listingIdToPricingAuditIds = it.listingIdToPricingAuditIds
                    )
                } else it
            }
        }
    }

    private suspend fun cartWithAvailableItems(
        cart: Cart,
        buyerOrgUnitId: String?,
        forceFetch: Boolean
    ): Cart {
        val availableItems = fetchAvailableItems(
            cart = cart,
            buyerOrgUnitId = buyerOrgUnitId,
            forceFetch = forceFetch
        )
        return cart.copy(items = availableItems)
    }

    override suspend fun getCartWithPromos(
        orgUnitId: String?,
        skipOOSItems: Boolean,
        fetchRiderDetails: Boolean,
        orderReadyForCheckout: Boolean
    ): DetailedCart? {
        return getDetailedCart(
            orgUnitId = orgUnitId,
            skipOOSItems = skipOOSItems,
            fetchLatestPrices = true,
            fetchRiderDetails = fetchRiderDetails,
            orderReadyForCheckout = orderReadyForCheckout
        )?.let { detailedCart ->
            val cart = detailedCart.cart
            val prePromoLeviesMap = fetchLevies(cart)
            val schemeDetails = schemesProvider.compute(this)
            val transformedItems = promotionsProvider.apply(this)?.let { promoContext ->
                promoContext.updateSchemeDetails(schemeDetails)
                setValue(CartContextKey.PROMOTION_CONTEXT, promoContext)
                promoContext.getTransformedItems()
            } ?: cart.items
            val transformedItemIds = transformedItems.map { it.id }
            logger.info("transformedItemIds: $transformedItemIds")
            val skippedItems = cart.items.filter { cartItem -> transformedItemIds.contains(cartItem.id).not() }
            logger.info("skippedItems: ${skippedItems.map { it.id }}")
            val transformedCart = if (skipOOSItems) {
                val freebieInCart = freebieItemsInCart(transformedItems).isNotEmpty()
                // if there is freebie in cart, we need to re-fetch the unavailability info and also check
                // for freebie availability
                if (freebieInCart) {
                    logger.info("freebie is present in cart during checkout - ${cart.id}")
                    val unavailabilityInfo = getUnavailabilityInfo(
                        buyerOrgUnitId = orgUnitId,
                        existingCart = cart.copy(items = transformedItems),
                        forceFetch = true
                    )
                    val unavailableItems = transformedItems.filter { cartItem ->
                        cartItem.product as ListingProductItem
                        unavailabilityInfo.find {
                            it.listingId == cartItem.product.listingId && it.salesUnitId == cartItem.product.salesUnitId
                        } != null
                    }
                    val totalUnavailableItemIds =
                        handleUnavailableFreebieItems(transformedItems, unavailableItems).map { it.id }
                    val availableItems = transformedItems.filterNot { it.id in totalUnavailableItemIds }
                    cart.copy(items = availableItems)
                } else {
                    cart.copy(items = transformedItems)
                }
            } else {
                cart.copy(items = transformedItems.plus(skippedItems))
            }
            val leviesMap = fetchLevies(transformedCart)
            cartWithPromos = DetailedCart(
                cart = transformedCart,
                leviesMap = leviesMap,
                prePromoCart = cart,
                prePromoLeviesMap = prePromoLeviesMap,
                itemsWithInvalidPrice = itemsWithInvalidPrice,
                listingIdToPricingAuditIds = detailedCart.listingIdToPricingAuditIds
            )
            cartWithPromos
        }
    }

    override suspend fun getPreloadedCartWithPromos(): DetailedCart? {
        return cartWithPromos
    }

    override suspend fun getBuyerOrg(): OrgV1.OrgAccountExtendedResponse? {
        return buyerOrg ?: getCart()?.let { cart ->
            buyerOrg = orgProvider.fetchOrgDetails(cart.buyerId).await()
            buyerOrg
        }
    }

    override suspend fun getBuyerOrgUnit(
        orgUnitId: String?
    ): ModelV1.OrgUnit? {
        return orgUnitId?.let { unitId ->
            if (unitId.isNotEmpty()) {
                fetchOrgUnit(unitId)
            } else getBuyerHomeOrgUnit()
        } ?: getBuyerHomeOrgUnit()
    }

    override suspend fun getBuyerOrgIdentityModel(): OrgInternalIdentityModel? {
        return kotlin.runCatching {
            val buyerId = buyerOrg?.orgAccount?.orgId ?: getCart()?.buyerId
            buyerId?.let {
                orgIdentityProvider.getOrgIdentityModel(it)
            }
        }.getOrElse { _ ->
            null
        }
    }

    override suspend fun getCartWithLatestPrices(
        orgUnitId: String?,
        fetchRiderDetails: Boolean,
        orderReadyForCheckout: Boolean
    ): Pair<Cart, Map<String, String>>? {
        return getCart()?.let { cart ->
            val buyerOrgUnitId = orgUnitId ?: getBuyerOrg()?.orgAccount?.headOfficeOrgUnitRef ?: ""
            val pincode = if (buyerOrgUnitId.isNotEmpty()) {
                getBuyerOrg()?.orgAccount?.orgUnitsMap?.get(buyerOrgUnitId)?.unitAddress?.pincode
            } else ""
            if (pincode.isNullOrBlank()) {
                logger.error("[getCartWithLatestPrices] Pincode = $pincode buyerOrg = " +
                    "${getBuyerOrg()?.orgAccount?.orgId} buyerOrgUnitId = $buyerOrgUnitId")
            }
            val listingSalesUnitRef = cart.items.map { item ->
                ListingSalesUnitRef(
                    listingId = (item.product as ListingProductItem).listingId,
                    salesUnitId = item.product.salesUnitId,
                    refId = item.id,
                )
            }
            val (pricingContext, duration) = measureTimedValue {
                pricingProvider.fetchPriceAsync(
                    PriceRequest(
                        orgId = cart.buyerId,
                        orgUnitId = buyerOrgUnitId,
                        pincode = pincode,
                        listingsMap = getListingsMap(),
                        listingSalesUnitRef = listingSalesUnitRef,
                        fetchRiderDetails = fetchRiderDetails,
                        orderReadyForCheckout = orderReadyForCheckout
                    )
                )
            }
            logger.info("Pricing duration: ${duration.toDouble(DurationUnit.MILLISECONDS)}")
            val (pricedItems, duration2) = measureTimedValue {
                cart.items.parallelMap { item ->
                    pricingContextInterpreter.applyPrice(pricingContext, item)
                }
            }
            val listingIdToPricingAuditIds = pricedItems.mapNotNull {
                it.pricingAuditId?.let { pricingAuditID ->
                    val productItem = it.item.product as ListingProductItem
                    Triple(productItem.listingId, productItem.salesUnitId, pricingAuditID)
                }
            }.associate { "${it.first}:${it.second}" to it.third }
            itemsWithInvalidPrice = cart.items.filter { item ->
                !pricingContextInterpreter.hasValidPrice(pricingContext, item)
            }
            this.packagingUnitsMap = pricedItems.associate {
                it.item.id to it.packagingUnit
            }
            if (fetchRiderDetails) {
                cart.items.parallelMap { item ->
                    val riderPromos = pricingContextInterpreter.getRiderPromos(pricingContext, item)
                    riderPromosMap[item.id] = riderPromos
                }
            }
            val items = pricedItems.map { it.item }
            logger.info("Pricing options duration: ${duration2.toDouble(DurationUnit.MILLISECONDS)}")
            Pair(cart.copy(items = items), listingIdToPricingAuditIds)
        }
    }

    override fun getItemsWithInvalidPrice(): List<CartItem> {
        return itemsWithInvalidPrice
    }

    override suspend fun getUnavailabilityInfo(
        buyerOrgUnitId: String?,
        existingCart: Cart?,
        forceFetch: Boolean
    ): List<ListingAvailability> {
        return if (forceFetch || inventoryLoaded.not()) {
            val fetchedCart = existingCart ?: getCart()
            fetchedCart?.let { cart ->
                getBuyerOrg()?.let { buyerOrg ->
                    val orgUnitId =
                        getBuyerOrgUnit(buyerOrgUnitId)?.orgUnitId ?: buyerOrg.orgAccount.headOfficeOrgUnitRef
                    val orgUnit = getBuyerOrgUnit(buyerOrgUnitId)
                        ?: buyerOrg.orgAccount.orgUnitsMap[buyerOrg.orgAccount.headOfficeOrgUnitRef]
                    val listingsMap = getListingsMap(cart)
                    val (activeListings, inactiveListings) = listingsMap.values.partition {
                        it.status == ModelV2.TradeListing.Status.ACTIVE
                    }
                    val inactiveListingIds = inactiveListings.map { it.listingId }
                    val activeListingIds = activeListings.map { it.listingId }
                    val inactiveItems = cart.items.filter { item ->
                        item.product as ListingProductItem
                        inactiveListingIds.contains(item.product.listingId)
                    }
                    val activeItems = cart.items.filter { item ->
                        item.product as ListingProductItem
                        activeListingIds.contains(item.product.listingId)
                    }
                    logger.info("Inactive listing ids: $inactiveListingIds")
                    logger.info("Inactive listing items: ${inactiveItems.map { it.id }}")
                    val availability = orgUnit?.let { buyerOrgUnit ->
                        activeItems.groupBy { (it.product as ListingProductItem).sellerId }
                            .flatMap { (sellerId, items) ->
                                logger.info("Item: ${items.map { (it.product as ListingProductItem).toString() + it.quantity }}")
                                inventoryProvider.checkAvailabilityAsync(
                                    sellerOrgId = sellerId,
                                    buyerOrgUnitId = orgUnitId,
                                    deliveryPincode = buyerOrgUnit.unitAddress.pincode,
                                    platform = cart.platformId,
                                    listingsData = items.map { item ->
                                        ListingData(
                                            (item.product as ListingProductItem).listingId,
                                            item.product.salesUnitId,
                                            item.quantity
                                        )
                                    }
                                ).await().run {
                                    logger.info("Availability res: ${this.values}")
                                    this.filter { (_, listingAvailability) ->
                                        listingAvailability.isAvailable.not()
                                    }
                                }.values.toList()
                            }
                    }
                    logger.info("Availability: $availability")
                    val availableItemsAfterInventoryAvailability = activeItems.filter { cartItem ->
                        cartItem.product as ListingProductItem
                        availability?.find {
                            it.listingId == cartItem.product.listingId && it.salesUnitId == cartItem.product.salesUnitId
                        } == null
                    }
                    val availabilityInCatalog =
                        getUnavailableItemsFromCatalog(availableItemsAfterInventoryAvailability, orgUnit)
                    logger.info("Availability from catalog: $availabilityInCatalog")
                    val inactiveListingAvailability = inactiveItems.map { item ->
                        item.product as ListingProductItem
                        ListingAvailability(
                            listingId = item.product.listingId,
                            salesUnitId = item.product.salesUnitId,
                            isAvailable = false,
                            availableQuantity = 0
                        )
                    }
                    val initialListingAvailability =
                        availability?.plus(availabilityInCatalog)?.plus(inactiveListingAvailability)
                        ?: availabilityInCatalog.plus(inactiveListingAvailability)
                    inventoryLoaded = true
                    listingAvailability = handleAvailabilityWithFreebieItems(initialListingAvailability, cart.items)
                    listingAvailability
                } ?: emptyList()
            } ?: emptyList()
        } else listingAvailability ?: emptyList()
    }

    override suspend fun getAvailableItems(
        existingCart: Cart?,
        buyerOrgUnitId: String?,
        forceFetch: Boolean
    ): List<CartItem> {
        val fetchedCart = existingCart ?: getCart()
        return fetchedCart?.let {
            fetchAvailableItems(
                cart = it,
                buyerOrgUnitId = buyerOrgUnitId,
                forceFetch = forceFetch
            )
        } ?: emptyList<CartItem>()
    }

    override suspend fun getListingsMap(): Map<String, ModelV2.TradeListing> {
        return getCart()?.let { cart ->
            getListingsMap(cart)
        } ?: emptyMap()
    }

    @Suppress("UNCHECKED_CAST")
    override suspend fun <T> getValue(key: CartContextKey): T? {
        return valuesMap[key]?.let {
            it as T
        }
    }

    override suspend fun getPackagingUnitDetails(itemId: String): PackagingUnit? {
        return packagingUnitsMap[itemId]
    }

    override suspend fun <T> setValue(key: CartContextKey, value: T) {
        valuesMap[key] = value as Any
    }

    override suspend fun getRiderPromos(): Map<String, Map<String, Int>> {
        return riderPromosMap
    }
    private suspend fun getBuyerHomeOrgUnit(): ModelV1.OrgUnit? {
        return getBuyerOrg()?.orgAccount?.let { orgAccount ->
            orgAccount.orgUnitsMap[orgAccount.headOfficeOrgUnitRef]
        }
    }

    private suspend fun getListingsMap(cart: Cart): Map<String, ModelV2.TradeListing> {
        return listingsMap?.let { lm ->
            // check if any listing is missing based on current order lines
            val missingListingOrderLines = cart.items.filterNot { item ->
                lm[(item.product as ListingProductItem).listingId]?.salesUnitList?.any {
                    it.salesUnitId == item.product.salesUnitId
                } ?: false
            }
            // find all order lines associated with missed listings
            val listingsPair = missingListingOrderLines.flatMap { item ->
                cart.items.filter { (item.product as ListingProductItem).listingId == (it.product as ListingProductItem).listingId }
                    .map {
                        (it.product as ListingProductItem).listingId to it.product.salesUnitId
                    }
            }.groupBy { it.first }.map { (listingId, salesUnitsList) ->
                listingId to salesUnitsList.map { it.second }
            }
            val missingListings = fetchListings(listingsPair)
            listingsMap = lm.plus(missingListings)
            listingsMap
        } ?: kotlin.run {
            listingsMap = cart.items.map {
                (it.product as ListingProductItem).listingId to it.product.salesUnitId
            }.groupBy { it.first }.map { (key, value) ->
                key to value.map { it.second }
            }.let { listingPairs ->
                fetchListings(listingPairs)
            }.toMap()
            listingsMap
        } ?: emptyMap()
    }

    private suspend fun fetchListings(
        listingPairs: List<Pair<String, List<String>>>
    ): Map<String, ModelV2.TradeListing> {
        return catalogProvider.getMultipleListings(listingPairs).associateBy { it.listingId }
    }

    /**
     * Temporary work around to use org repository directly as OrgProvider dep is not resolving fetchOrgUnit method
     */
    private suspend fun fetchOrgUnit(orgUnitId: String): ModelV1.OrgUnit {
        return orgRepository.getOrgUnit(orgUnitId).await()
    }

    // Call catalog API for inventory availability only for meat items - Remove this code after complete meat
    // migration to first party
    private suspend fun getUnavailableItemsFromCatalog(
        cartItems: List<CartItem>,
        orgUnit: ModelV1.OrgUnit?
    ): List<ListingAvailability> {
        val listingMap = getListingsMap()
        val verticals = listingMap.values.map { it.vertical }.distinct()
        val meatVerticals = verticals.filter { verticalCache.getVerticalAsync(it).await().isMeat() }
        val meatListingIds = listingMap.values.filter { it.vertical in meatVerticals }.map { it.listingId }
        val meatCartItems = cartItems.filter {
            it.product as ListingProductItem
            it.product.listingId in meatListingIds
        }
        return if (meatCartItems.isNotEmpty() && meatMigrationToFirstPartyExpEnabled(orgUnit).not()) {
            meatCartItems.chunked(catalogBatch).map { inventoryDetailsChuck ->
                val availabilityRequest = inventoryDetailsChuck.map { inventoryDetail ->
                    inventoryDetail.product as ListingProductItem
                    ApiV2.AvailabilityRequest.newBuilder().let {
                        it.setListingId(inventoryDetail.product.listingId)
                            .setSalesUnitId(inventoryDetail.product.salesUnitId)
                            .setNumUnits(inventoryDetail.quantity)
                        it.build()
                    }
                }
                catalogServiceClient.checkAvailability(
                    ApiV2.AvailabilityBatchRequest.newBuilder().addAllRequest(availabilityRequest).build()
                ).execute()
            }.parallelMap(PARALLELISM_FACTOR) { batchResponseFuture ->
                val notFoundList = batchResponseFuture.await().notFoundIdList
                require(notFoundList.size == 0) { "Found invalid sales unit ids: $notFoundList" }
                batchResponseFuture.await().responseMap.entries.filterNot { it.value.isAvailable }.map { entry ->
                    ListingAvailability(
                        listingId = entry.value.listingId,
                        salesUnitId = entry.value.salesUnitId,
                        availableQuantity = entry.value.availableQty.toLong(),
                        isAvailable = false
                    )
                }
            }.flatten()
        } else {
            emptyList()
        }
    }

    // Remove this code after complete meat migration to first party
    private suspend fun meatMigrationToFirstPartyExpEnabled(buyerOrgUnit: ModelV1.OrgUnit?): Boolean {
        return try {
            if (null != buyerOrgUnit) {
                val buyerHub = hubProvider.getHubOrgUnitIdForBuyer(
                    category = "meat",
                    platform = ModelV1.SellingPlatform.UDAAN_MARKETPLACE,
                    pincode = buyerOrgUnit.unitAddress.pincode,
                    buyerOrgUnitId = buyerOrgUnit.orgUnitId
                )
                buyerHub?.let {
                    logger.info("[CartContext - getUnavailableItemsFromCatalog] BuyerHub = $it")
                    isHubPartOfExperiment(it)
                } ?: run {
                    logger.info("[CartContext - getUnavailableItemsFromCatalog] Unable to find hub info for " +
                        "pincode ${buyerOrgUnit.unitAddress.pincode} and orgUnitId ${buyerOrgUnit.orgUnitId}")
                    false
                }
            } else {
                logger.info("[CartContext - getUnavailableItemsFromCatalog] Unable to find buyerOrgUnit " +
                    "for the buyer ")
                false
            }
        } catch (ex: Exception) {
            logger.info("[CartContext - getUnavailableItemsFromCatalog] Exception while verifying if buyerHub is " +
                "part of experiment - ${ex.message} - $ex")
            false
        }
    }

    // Remove this code after complete meat migration to first party
    private suspend fun isHubPartOfExperiment(hubId: String): Boolean {
        return kotlin.runCatching {
            val config = configServiceHelper.getValueByConfigName(MEAT_TO_FIRST_PARTY_EXP)?.let { config ->
                objectMapper.readValue(config, ConfigManager::class.java)
            }
            val configsMatchingHubs =
                config?.configList?.filter { it.hubIds.contains(hubId) || it.hubIds.contains("ALL") } ?: emptyList()
            return if (configsMatchingHubs.isNotEmpty()) {
                val currentTime = System.currentTimeMillis()
                configsMatchingHubs.firstOrNull { hubConfig ->
                    val endCondition = hubConfig.endTime?.let { it > currentTime } ?: true
                    hubConfig.startTime < currentTime && endCondition
                } != null
            } else {
                false
            }
        }.getOrElse { exception ->
            logger.error("Failed to read meat migration to first party config", exception)
            false
        }
    }

    // Remove this code after complete meat migration to first party
    private data class ConfigManager(
        val configList: List<Config>
    )

    // Remove this code after complete meat migration to first party
    private data class Config(
        val hubIds: List<String>,
        val startTime: Long,
        val endTime: Long?
    )

    private suspend fun fetchAvailableItems(
        cart: Cart,
        buyerOrgUnitId: String?,
        forceFetch: Boolean
    ): List<CartItem> {
        return if (forceFetch || availableItemsLoaded.not()) {
            val unavailabilityInfo = getUnavailabilityInfo(buyerOrgUnitId, cart)
            logger.info("[Cart Context] Unavailability map: $unavailabilityInfo")
            getCartWithLatestPrices(orgUnitId = buyerOrgUnitId)
            val cartItemIdsWithInvalidPrice = getItemsWithInvalidPrice().map { it.id }
            val availableItems = cart.items.filterNot { cartItem ->
                cartItem.product as ListingProductItem
                val unavailableWithInventory = unavailabilityInfo.find {
                    it.listingId == cartItem.product.listingId && it.salesUnitId == cartItem.product.salesUnitId
                } != null
                val unavailableWithPrice = cartItemIdsWithInvalidPrice.contains(cartItem.id)
                unavailableWithInventory || unavailableWithPrice
            }
            val availableItemsAfterFreebies = handleUnavailableFreebieItemsInCart(cart.items, availableItems)
            availableCartItems = availableItemsAfterFreebies
            availableItemsLoaded = true
            logger.info("[fetchAvailableItems] fetchAvailableItems (data from external services) =" +
                " $availableItemsAfterFreebies")
            availableItemsAfterFreebies
        } else {
            logger.info("[fetchAvailableItems] fetchAvailableItems (data from CartContext) = $availableCartItems")
            availableCartItems
        }
    }

    // Currently unavailableItems will have only normal case i.e., non-freebie case
    // In case of freebie, if either is available and other isn't, treat the other as unavailable
    private fun handleUnavailableFreebieItemsInCart(
        cartItems: List<CartItem>,
        availableCartItems: List<CartItem>
    ): List<CartItem> {
        val parentFreebieItemIdMap = fetchParentItemIdFreebieItemIdMap(cartItems)
        val availableCartItemIds = availableCartItems.map { it.id }
        val unavailableItems = parentFreebieItemIdMap.map { (parentCartItemId, freebieCartItemId) ->
            when {
                availableCartItemIds.contains(parentCartItemId) and
                    availableCartItemIds.contains(freebieCartItemId).not() -> parentCartItemId
                availableCartItemIds.contains(freebieCartItemId) and
                    availableCartItemIds.contains(parentCartItemId).not() -> freebieCartItemId
                else -> null
            }
        }
        return availableCartItems.filterNot { it.id in unavailableItems }
    }

    private suspend fun fetchLevies(cart: Cart): Map<String, List<ItemLevy>> {
        val levyItemReqs = cart.items.map { item ->
            (item.product as ListingProductItem)
            LevyItemReq(
                id = item.id,
                listingId = item.product.listingId,
                salesUnitId = item.product.salesUnitId,
                quantity = item.quantity,
                amountInPaise = item.quantity * item.perUnitAmountInPaise,
            )
        }
        val leviesMapRes = levyProvider.getLevies(
            buyerId = cart.buyerId,
            levyItemReqs = levyItemReqs,
            listingsMap = getListingsMap(),
            categoryGroupId = CategoryGroupV2.FoodAndFMCG.id, // TODO: Fix it
            platformId = cart.platformId,
        )
        return when (leviesMapRes) {
            is Either.Left -> throw LevyGenerationException("Failed to generate taxes")
            is Either.Right -> leviesMapRes.value
        }
    }

    // Current availability checks mark item unavailable incase it is isn't available
    // This function will ensure to mark parent inventory is unavailable in case freebie is unavailable
    private fun handleAvailabilityWithFreebieItems(
        initialListingAvailability: List<ListingAvailability>,
        cartItems: List<CartItem>
    ): List<ListingAvailability> {
        val unavailableListingIds = initialListingAvailability.map { it.listingId }
        val listingAvailabilityList = initialListingAvailability.toMutableList()
        val freebieCartItemIdParentCartItemIdMap = fetchFreebieItemIdParentItemIdMap(cartItems)
        freebieCartItemIdParentCartItemIdMap.map { (freebieCartItemId, parentCartItemId) ->
            // if freebie item is unavailable but parent is available, move parent to unavailable section
            // & remove freebie item
            val freebieItem = cartItems.firstOrNull { it.id == freebieCartItemId} ?: return@map
            val parentItem = cartItems.firstOrNull { it.id == parentCartItemId} ?: return@map
            freebieItem.product as ListingProductItem
            parentItem.product as ListingProductItem
            if ((freebieItem.product.listingId in unavailableListingIds) &&
                (parentItem.product.listingId !in unavailableListingIds)) {
                listingAvailabilityList.add(
                    ListingAvailability(
                        listingId = parentItem.product.listingId,
                        salesUnitId = parentItem.product.salesUnitId,
                        isAvailable = false,
                        availableQuantity = 0
                    )
                )
                val freebieListingAvailability =
                    listingAvailabilityList.firstOrNull {
                        it.listingId == freebieItem.product.listingId &&
                                it.salesUnitId == freebieItem.product.salesUnitId }
                freebieListingAvailability?.let {
                    listingAvailabilityList.remove(it)
                }
            }
        }
        return listingAvailabilityList.toList()
    }

    // Currently unavailableItems will have only parentItems.
    // In case of freebie, if parent is unavailable, we should also mark the freebie as unavailable
    private fun handleUnavailableFreebieItems(
        cartItems: List<CartItem>,
        unavailableCartItems: List<CartItem>
    ): List<CartItem> {
        val parentFreebieItemIdMap = fetchParentItemIdFreebieItemIdMap(cartItems)
        val unavailableFreebieItems = unavailableCartItems.mapNotNull { cartItemId ->
            val freebieItemId = parentFreebieItemIdMap.get(cartItemId.id)
            freebieItemId?.let {
                cartItems.first { it.id == freebieItemId }
            }
        }
        val totalUnavailableItems = unavailableCartItems + unavailableFreebieItems
        segregateAndLogUnavailableItems(totalUnavailableItems)
        return totalUnavailableItems
    }

    // To track the unavailable items during checkout call
    private fun segregateAndLogUnavailableItems(unavailableCartItems: List<CartItem>) {
        val parentFreebieItemIdMap = fetchParentItemIdFreebieItemIdMap(unavailableCartItems)
        val unavailableParentItemIds = parentFreebieItemIdMap.keys
        val unavailableFreebieItemIds = parentFreebieItemIdMap.values
        val unavailableCartItemIds =
            unavailableCartItems.map { it.id } - (unavailableParentItemIds + unavailableFreebieItemIds)
        if (unavailableCartItemIds.isNotEmpty()) {
            logUnavailableItems(unavailableCartItems, unavailableCartItemIds, INDEPENDENT)
        }
        if (unavailableParentItemIds.isNotEmpty()) {
            logUnavailableItems(unavailableCartItems, unavailableParentItemIds.toList(), PARENT)
        }
        if (unavailableFreebieItemIds.isNotEmpty()) {
            logUnavailableItems(unavailableCartItems, unavailableFreebieItemIds.toList(), FREEBIE)
        }
    }

    private fun logUnavailableItems(
        totalUnavailableCartItems: List<CartItem>,
        unavailableCartItemIds:List<String>,
        type: String
    ) {
        val buyerId = this.buyerCart?.buyerId
        val cartId = this.cartId
        unavailableCartItemIds.map { unavailableItemId ->
            val unavailableCartItem = totalUnavailableCartItems.first { it.id == unavailableItemId }
            unavailableCartItem.product as ListingProductItem
            logger.info("CART_CHECKOUT::INVENTORY_UNAVAILABLE ${
                mapOf(
                    "orgId" to buyerId.toString(),
                    "cartId" to cartId,
                    "unavailableCartItemId" to unavailableItemId,
                    "listingId" to unavailableCartItem.product.listingId,
                    "itemType" to type
                )
            }"
            )
        }
    }

    // Get freebie cart item Id - parent cart item id map
    private fun fetchFreebieItemIdParentItemIdMap(
        cartItems: List<CartItem>
    ): Map<String, String> {
        val freebieItems = freebieItemsInCart(cartItems)
        return freebieItems.map { freebieItem ->
            freebieItem.product as ListingProductItem
            val parentCartItemId = freebieItem.product.freebieInfo?.parentCartLineId!!
            freebieItem.id to parentCartItemId
        }.toMap()
    }

    // Get parent cart item id - freebie cart item Id map
    private fun fetchParentItemIdFreebieItemIdMap(
        cartItems: List<CartItem>
    ): Map<String, String> {
        val freebieParentMap = fetchFreebieItemIdParentItemIdMap(cartItems)
        return freebieParentMap.entries.associate { (freebieItemId, parentItemId) -> parentItemId to freebieItemId }
    }

    // Get the freebie items in the cart
    private fun freebieItemsInCart(cartItems: List<CartItem>): List<CartItem> {
        return cartItems.filter { cartItem ->
            cartItem.isFreebie()
        }
    }

}
